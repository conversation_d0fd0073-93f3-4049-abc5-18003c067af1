<template>
  <div class="basedOnTheCode box" ref="box" id="BasedOnTheCode">
    <div class="left" id="LeftWrap">
      <p>基础代码维护</p>
      <el-menu
        ref="elMenu_Ref"
        :default-active="muenId"
        :defaultOpeneds="defaultOpened"
        class="el-menu-vertical-demo"
        @open="handleOpen"
        @close="handleClose"
        @select="handleSelect"
        style="height: calc(100% - 50px); overflow: auto"
      >
        <el-submenu :index="item.id" v-for="item in muenList" :key="item.id">
          <template slot="title">
            <i :class="item.icon" class="icon"></i>
            <span>{{ item.title }}</span>
          </template>

          <template v-for="child in item.children">
            <el-submenu
              v-if="child.children.length > 0"
              :index="child.id"
              :key="child.id"
            >
              <template slot="title">
                <i
                  :class="
                    openedMenus.includes(child.id)
                      ? 'iconfont icon-wenjianjia'
                      : 'iconfont icon-folder1-fill'
                  "
                  class="icon"
                ></i>
                <i
                  class="iconfont icon-shezhiduiying icons"
                  v-show="child.id === '2-5' || child.id === '2-4'"
                ></i>
                <span>{{ child.title }}</span>
              </template>
              <el-menu-item
                :index="grandson.id"
                v-for="grandson in child.children"
                :key="grandson.id"
                @click="goTo(grandson)"
              >
                <template slot="title">
                  <i
                    class="iconfont icon-biaodan icons"
                    v-show="child.id === '5-3'"
                  ></i>
                  <i
                    class="iconfont icon-shezhiduiying icons"
                    v-show="child.id === '2-4' || child.id === '2-5'"
                  ></i>
                  <span>{{ grandson.title }}</span>
                </template>
              </el-menu-item>
            </el-submenu>
          </template>

          <template v-for="child in item.children">
            <el-menu-item
              v-if="child.children.length == 0"
              :key="child.id"
              :index="child.id"
              @click="goTo(child)"
            >
              <template slot="title">
                <i :class="child.icon" class="icons"></i>
                <span>{{ child.title }}</span>
              </template>
            </el-menu-item>
          </template>
        </el-submenu>
      </el-menu>
    </div>
    <!-- <div class="resize" title="收缩侧边栏">⋮</div> -->
    <div class="right">
      <transition name="fade-transform" mode="out-in">
        <component
          ref="viewConfig"
          :is="path"
          :clsCode="clsCode"
          :claName="claName"
          :tableThead="tableThead"
          :viewConfig="viewConfig"
          :allInfo="drawerInfo"
        />
      </transition>
    </div>
  </div>
</template>

<script lang="ts">
import departInformation from './commonCode/departInformation.vue'; //科室信息
import checkPlaceInfo from './commonCode/checkPlaceInfo.vue'; //检查地址信息
import hospitalInfo from './commonCode/hospitalInfo.vue'; //院区信息
import jobTypeInfo from './commonCode/jobTypeInfo.vue'; //职业工种信息
import nationInfo from './commonCode/nationInfo.vue'; //民族信息
import nativeInfo from './commonCode/nativeInfo.vue'; //国籍信息
import sampleInfo from './commonCode/sampleInfo.vue'; //标本信息
import followUpTemplate from './commonCode/followUpTemplate.vue'; //随访模版选项
import hisChargeInfo from './commonCode/hisChargeInfo.vue'; // his收费项目信息

import basicInformation from './chargeCode/basicInformation.vue'; // 基础分类信息
import projectInformation from './chargeCode/projectInformation.vue'; // 项目分类信息
import feeTypeInformation from './chargeCode/feeTypeInformation.vue'; // 费别信息
import payInformation from './chargeCode/payInformation.vue'; // 支付方式信息
import ruleInformation from './chargeCode/ruleInformation.vue'; // 凑整规则信息
import tjTypeMain from './physicalExamination/tjTypeMain.vue'; //体检分类-主检/审核人员对应
//      体检类
import classifyInformation from './physicalExamination/classifyInformation.vue'; // 项目分类信息
import barcodeClassification from './physicalExamination/barcodeClassification.vue'; //条码分类
import referenceRangeInfo from './physicalExamination/referenceRangeInfo.vue'; // 参考范围信息
import physicalExaminationItem from './physicalExamination/physicalExaminationItem.vue'; //体检项目信息
import shareThePage from './physicalExamination/shareThePage.vue'; // 体检组合信息
import physicalExaminationSetMeal from './physicalExamination/physicalExaminationSetMeal.vue'; // 体检套餐信息
import archiveItems from './physicalExamination/archiveItems.vue'; // 档案项目信息
import noCommonCombination from './physicalExamination/noCommonCombination.vue'; // 不共用组合信息
import conclusion from './physicalExamination/conclusion.vue'; // 体检结论模板
import normalResult from './physicalExamination/normalResult.vue'; // 正常结果表

import otherCodeMapping from './otherCodeMapping/otherCodeMapping.vue'; // 其他代码对应

import majorPositiveList from './majorPositive/majorPositiveList.vue'; //重大阳性列表
import majorPositiveKeywordsList from './majorPositive/majorPositiveKeywordsList.vue'; // 重大阳性关键字
import baseCodeOccupationalMeal from './occDiseases/components/baseCodeOccupationalMeal.vue'; // 职业病套餐维护组件
import { mapGetters } from 'vuex';
import Vue from 'vue';

import { ajax as $ajax } from '@/common/ajax.js';
import { apiUrls as $apiUrls } from '@/common/apiUrls.ts';

export default Vue.extend({
  name: 'basedOnTheCode',
  components: {
    departInformation,
    checkPlaceInfo,
    hospitalInfo,
    jobTypeInfo,
    nationInfo,
    nativeInfo,
    sampleInfo,
    hisChargeInfo,
    barcodeClassification,
    classifyInformation,
    referenceRangeInfo,
    physicalExaminationItem,
    shareThePage,
    physicalExaminationSetMeal,
    archiveItems,
    noCommonCombination,
    conclusion,
    normalResult,
    basicInformation,
    projectInformation,
    feeTypeInformation,
    payInformation,
    ruleInformation,
    otherCodeMapping,
    tjTypeMain,
    majorPositiveList,
    majorPositiveKeywordsList,
    followUpTemplate,
    baseCodeOccupationalMeal
  },
  computed: {
    ...mapGetters(['G_config'])
  },
  data() {
    return {
      path: 'departInformation',
      muenId: '1-1',
      defaultOpened: ['1'],
      clsCode: '',
      claName: '',
      openedMenus: [],
      muenList: [
        {
          id: '1',
          title: '公共类代码',
          icon: 'iconfont icon-leixing1',
          children: [
            {
              id: '1-1',
              title: '科室信息',
              icon: 'iconfont icon-biaodan',
              path: 'departInformation',
              children: []
            },
            {
              id: '1-2',
              title: '采集地点信息',
              icon: 'iconfont icon-biaodan',
              path: 'checkPlaceInfo',
              children: []
            },
            {
              id: '1-3',
              title: '职业工种信息',
              icon: 'iconfont icon-biaodan',
              path: 'jobTypeInfo',
              children: []
            },
            {
              id: '1-4',
              title: '民族信息',
              icon: 'iconfont icon-biaodan',
              path: 'nationInfo',
              children: []
            },
            {
              id: '1-5',
              title: '籍贯信息',
              icon: 'iconfont icon-biaodan',
              path: 'nativeInfo',
              children: []
            },
            {
              id: '1-6',
              title: '标本信息',
              icon: 'iconfont icon-biaodan',
              path: 'sampleInfo',
              children: []
            },
            {
              id: '1-7',
              title: '随访模版选项',
              icon: 'iconfont icon-biaodan',
              path: 'followUpTemplate',
              children: []
            },
            {
              id: '1-8',
              title: '院区代码信息',
              icon: 'iconfont icon-biaodan',
              path: 'hospitalInfo',
              children: []
            }
            // {
            //   id: "1-8",
            //   title: "His收费项目信息",
            //   icon: "iconfont icon-biaodan",
            //   path: "hisChargeInfo",
            //   children: []
            // }
          ]
        },
        {
          id: '2',
          title: '体检类代码',
          icon: 'iconfont icon-leixing1',
          children: [
            {
              id: '2-1',
              title: '项目分类信息',
              icon: 'iconfont icon-biaodan',
              path: 'classifyInformation',
              children: []
            },
            {
              id: '2-2',
              title: '条码分类信息',
              icon: 'iconfont icon-biaodan',
              path: 'barcodeClassification',
              children: []
            },
            {
              id: '2-3',
              title: '参考范围类型信息',
              icon: 'iconfont icon-biaodan',
              path: 'referenceRangeInfo',
              children: []
            },
            {
              id: '2-4',
              title: '体检项目信息',
              icon: 'iconfont icon-biaodan',
              path: 'physicalExaminationItem',
              children: [
                {
                  id: '2-4-0',
                  title: '全部',
                  path: 'shareThePage',
                  clsCode: '',
                  viewConfig: {
                    viewInfo: {
                      drawerTheads: {
                        lisItemCode: '检验项目代码',
                        lisItemCNName: '检验项目名称',
                        lisCombCode: '检验组合代码',
                        lisCombCNName: '检验组合名称'
                      },
                      title1: '检验项目列表',
                      title2: '',
                      btnName: '检验对应',
                      isItem: true, //项目分类?专业组///EnumData/Department
                      isSel: false,
                      isMterials: false, //是否显示材料费
                      isHead: false, //是否显示步
                      active: 2, //显示哪个页面
                      isOnly: true,
                      param: {
                        pageSize: 'pageSize',
                        pageNumber: 'pageNumber',
                        itemCode: 'itemCode',
                        keyword: 'keyword'
                        // itemCode: "itemCode",
                        // itemName: "itemName",
                        // lisItemCode: "lisItemCode",
                        // lisItemName: "lisItemName",
                        // lisRoomCode: "lisRoomCode",
                        // lisRoomName: "lisRoomName"
                      },
                      order: `item.lisItemCode?.includes(searchVal) ||
                      item.lisItemCNName?.includes(searchVal)`, //第二页输入框搜索
                      orderSel: `item.lisRoomCode?.includes(searchVal)`, //第二页下拉框搜索
                      setInfos: { itemCode: '', itemName: '' }, //点击对应详情传参
                      setInfo: { itemCode: '' }, //点击行传参
                      apiUrls: {
                        optionPort: '/EnumData/ItemCls', //下拉
                        leftPort: $apiUrls.GetCodeLisItems, //左边表格
                        rightPort: $apiUrls.GetMapCodeLisItems, //右边表格(query:combCode)
                        del: $apiUrls.RemoveMapCodeLisItems,
                        set: $apiUrls.SaveMapCodeLisItems
                      }
                    }
                  }
                }
              ]
            },
            {
              id: '2-5',
              title: '体检组合信息',
              icon: 'iconfont icon-shezhiduiying',
              children: [
                {
                  id: '2-5-0',
                  title: '全部',
                  path: 'physicalExaminationItem',
                  clsCode: '',
                  viewConfig: {
                    viewInfo: {
                      drawerTheads: {
                        itemCode: '项目代码',
                        itemName: '项目名称',
                        clsName: '项目分类'
                      },
                      title1: '体检项目列表',
                      title2: '',
                      btnName: '项目对应',
                      isItem: true, //项目分类?专业组///EnumData/Department
                      isSel: true,
                      isMterials: false, //是否显示材料费
                      isHead: false, //是否显示步
                      active: 2, //显示哪个页面
                      param: {
                        itemCode: 'itemCode',
                        clsCode: 'clsCode'
                      },
                      order: `item.itemCode?.includes(searchVal) ||
                      item.itemName?.includes(searchVal)`,
                      orderSel: `item.clsCode?.includes(searchVal)`,
                      setInfos: { combCode: '' }, //点击行传参
                      setInfo: { combCode: '' }, //点击行传参
                      apiUrls: {
                        optionPort: '/EnumData/ItemCls', //下拉
                        leftPort: '/EnumData/Item_ItemCls', //左边表格
                        rightPort: '/CodeMapping/pageQuery_ItemComb', //右边表格(query:combCode)
                        del: '/CodeMapping/CD_ItemComb/Delete',
                        set: '/CodeMapping/CD_ItemComb/Create'
                      }
                    }
                  }
                }
              ]
            },
            {
              id: '2-6',
              title: '体检套餐信息',
              icon: 'iconfont icon-shezhiduiying',
              path: 'physicalExaminationSetMeal',
              children: [],
              viewConfig: {
                viewInfo: {
                  drawerTheads: {
                    // clsName: "项目分类",
                    combCode: '组合代码',
                    combName: '组合名称',
                    sex: '性别',
                    price: '单价',
                    hisCode: 'his代码'
                  },
                  rightTheads: {
                    // clsName: "项目分类",
                    combCode: '组合代码',
                    combName: '组合名称',
                    sex: '性别',
                    originalPrice: '单价',
                    discount: '折扣',
                    price: '折后价'
                    // hisCode: "his代码",
                  },
                  title1: '体检组合项目列表',
                  title2: '',
                  isItem: true, //项目分类?专业组///EnumData/Department
                  isSel: true,
                  isRightTheads: true, //右边表头
                  isDiscount: true, //是否显示批折扣
                  isMterials: true, //是否显示材料费
                  isHead: false, //是否显示步
                  isCopy: true, //是否显示复制按钮
                  active: 2, //显示哪个页面
                  cell_red: ['price'],
                  param: {
                    combCode: 'combCode'
                  },
                  order: `item.combCode?.toLowerCase().includes(searchVal) ||
                  item.combName?.toLowerCase().includes(searchVal) || item.pinYinCode?.toLowerCase().includes(searchVal)`,
                  orderSel: `item.clsCode?.includes(searchVal)`,
                  setInfos: { clusCode: '' }, //点击行传参
                  setInfo: { clusCode: '' }, //点击行传参
                  apiUrls: {
                    optionPort: '/EnumData/ItemCls', //下拉
                    leftPort: '/EnumData/ItemComb_ItemCls', //左边表格
                    rightPort: '/CodeMapping/Query_ClusterComb', //右边表格(query:clusCode)
                    del: '/CodeMapping/CD_ClusterComb/Delete',
                    set: '/CodeMapping/CD_ClusterComb/Create'
                  }
                }
              }
            },
            {
              id: 'codeOccupationalMeal',
              title: '职业病套餐',
              icon: 'iconfont icon-biaodan',
              path: 'baseCodeOccupationalMeal',
              children: [],
              viewConfig: {
                viewInfo: {
                  drawerTheads: {
                    // clsName: "项目分类",
                    combCode: '组合代码',
                    combName: '组合名称',
                    sex: '性别',
                    price: '单价',
                    hisCode: 'his代码'
                  },
                  rightTheads: {
                    // clsName: "项目分类",
                    combCode: '组合代码',
                    combName: '组合名称',
                    sex: '性别',
                    originalPrice: '单价',
                    discount: '折扣',
                    price: '折后价'
                    // hisCode: "his代码",
                  },
                  title1: '体检组合项目列表',
                  title2: '',
                  isItem: true, //项目分类?专业组///EnumData/Department
                  isSel: true,
                  isRightTheads: true, //右边表头
                  isDiscount: true, //是否显示批折扣
                  isMterials: true, //是否显示材料费
                  isHead: false, //是否显示步
                  active: 2, //显示哪个页面
                  cell_red: ['price'],
                  param: {
                    combCode: 'combCode'
                  },
                  order: `item.combCode?.includes(searchVal) ||
                  item.combName?.includes(searchVal)`,
                  orderSel: `item.clsCode?.includes(searchVal)`,
                  setInfos: { clusCode: '' }, //点击行传参
                  setInfo: { clusCode: '' }, //点击行传参
                  apiUrls: {
                    optionPort: '/EnumData/ItemCls', //下拉
                    leftPort: '/EnumData/ItemComb_ItemCls', //左边表格
                    rightPort: '/CodeMapping/Query_ClusterComb', //右边表格(query:clusCode)
                    del: '/CodeMapping/CD_ClusterComb/Delete',
                    set: '/CodeMapping/CD_ClusterComb/Create'
                  }
                }
              }
            },
            {
              id: '2-7',
              title: '档案项目信息',
              icon: 'iconfont icon-shezhiduiying',
              path: 'archiveItems',
              children: [],
              viewConfig: {
                viewInfo: {
                  drawerTheads: {
                    itemCode: '体检项目代码',
                    itemName: '体检项目名称',
                    clsName: '体检项目分类'
                  },
                  title1: '体检项目列表',
                  title2: '',
                  isItem: true, //项目分类?专业组///EnumData/Department
                  isSel: true,
                  isMterials: false, //是否显示材料费
                  isHead: false, //是否显示步
                  active: 2, //显示哪个页面
                  isArchive: 1, //是否显示档案复选框
                  param: {
                    itemCode: 'itemCode'
                  },
                  order: `item.itemCode?.includes(searchVal) ||
                  item.clsName?.includes(searchVal)`,
                  orderSel: `item.clsCode?.includes(searchVal)`,
                  setInfos: { archiveCode: '', archiveName: '' }, //点击行传参
                  setInfo: { archiveCode: '' }, //点击行传参
                  apiUrls: {
                    optionPort: '/EnumData/ItemCls', //下拉
                    leftPort: '/EnumData/Item_ItemCls', //左边表格
                    rightPort: '/CodeMapping/PageQuery_MapArchiveItem', //右边表格(query:itemCode)
                    del: '/CodeMapping/CD_MapArchiveItem/Delete',
                    set: '/CodeMapping/CD_MapArchiveItem/Create'
                  }
                }
              }
            },
            {
              id: '2-8',
              title: '互斥组合设置',
              icon: 'iconfont icon-shezhiduiying',
              path: 'noCommonCombination',
              children: [],
              viewConfig: {
                id: '1',
                theads: {
                  mutexCode: '互斥代码',
                  mutexName: '互斥名称',
                  mutexCombs: '互斥组合展示'
                },
                operation: '互斥组合对应',
                apiUrl: {
                  delete: '/BasicCode/DeletMutexCode',
                  get: '/BasicCode/ReadMutexCode'
                },
                cell_blue: ['mutexName'],
                order: `item.mutexCode?.includes(searchVal) ||
                        item.mutexName?.includes(searchVal)`,
                viewInfo: {
                  drawerThead: {
                    mutexCode: '互斥代码',
                    mutexName: '互斥名称',
                    mutexCombs: '互斥组合展示'
                  },
                  drawerTheads: {
                    clsName: '项目分类',
                    combCode: '组合代码',
                    combName: '组合名称',
                    price: '单价',
                    hisCode: 'his代码'
                  },
                  rightTheads: {
                    combCode: '组合代码',
                    combName: '组合名称',
                    isPrimary: '是否主组合'
                  },
                  title: '',
                  title1: '体检对应组合列表',
                  title2: '',
                  isItem: true, //项目分类?专业组///EnumData/Department
                  isSel: true,
                  isMterials: false, //是否显示材料费
                  isHead: true, //是否显示步
                  active: 1, //显示哪个页面
                  isRightTheads: true, //右边表头
                  param: {
                    combCode: 'combCode',
                    combName: 'combName'
                  },
                  sel1: '填写互斥组合信息',
                  sel2: '选择体检对应组合',
                  cell_red: ['price'],
                  orders: `item.combCode?.includes(searchVal) ||
                  item.combName?.includes(searchVal)`,
                  orderSels: `item.clsCode?.includes(searchVal)`,
                  order: `item.combCode?.includes(searchVal) ||
                  item.combName?.includes(searchVal)`,
                  orderSel: `item.clsCode?.includes(searchVal)`,
                  rightOrder: `item.combCode?.includes(searchVal) ||
                  item.combName?.includes(searchVal)`,
                  setInfos: { mutexCode: '', mutexName: '' }, //点击行传参
                  setInfo: { mutexCode: '' }, //点击行传参
                  apiUrls: {
                    optionPort: '/EnumData/ItemCls', //下拉
                    // viewPort: "/BasicCode/ReadMutexCombGroups", //页面一
                    leftPort: '/EnumData/ItemComb_ItemCls', //左边表格
                    rightPort: '/BasicCode/ReadMutexCodeMapComb', //右边表格(query:mutexCode)
                    del: '/BasicCode/MutexCodeMapComb/Delete',
                    set: '/BasicCode/MutexCodeMapComb/Create'
                  }
                }
              }
            },
            {
              id: '2-9',
              title: '体检结论模板',
              icon: 'iconfont icon-biaodan',
              path: 'conclusion',
              children: []
            },
            {
              id: '2-10',
              title: '综述过滤小结列表',
              icon: 'iconfont icon-biaodan',
              path: 'normalResult',
              children: []
            },
            {
              id: '2-11',
              title: '体检分类-主检/审核人员对应',
              icon: 'iconfont icon-shezhiduiying',
              path: 'tjTypeMain',
              children: []
            }
          ]
        },
        {
          id: '3',
          title: '收费类代码',
          icon: 'iconfont icon-leixing1',
          children: [
            {
              id: '3-1',
              title: '基础分类信息',
              icon: 'iconfont icon-biaodan',
              path: 'basicInformation',
              children: []
            },
            {
              id: '3-2',
              title: '项目分类信息',
              icon: 'iconfont icon-shezhiduiying',
              path: 'projectInformation',
              children: [],
              viewConfig: {
                viewInfo: {
                  drawerTheads: {
                    feeClsCode: '基础分类代码',
                    feeClsName: '基础分类名称'
                  },
                  title1: '基础分类列表',
                  title2: '',
                  isItem: true, //项目分类?专业组///EnumData/Department
                  isSel: false,
                  isMterials: false, //是否显示材料费
                  isHead: false, //是否显示步
                  active: 2, //显示哪个页面
                  param: {
                    feeClsCode: 'feeClsCode',
                    feeClsName: 'feeClsName'
                  },
                  order: `item.feeClsCode?.includes(searchVal) ||
                  item.feeClsName?.includes(searchVal)`,
                  orderSel: ``,
                  setInfos: {
                    feeClsGroupCode: '',
                    feeClsGroupName: '',
                    feeClsGroupType: ''
                  },
                  setInfo: { feeClsGroupCode: '' }, //点击行传参
                  apiUrls: {
                    leftPort: '/BasicCode/RD_CodeFeeCls/Read', //左边表格
                    rightPort: '/CodeMapping/PageQuery_MapFeeClsGroupFeeCls', //右边表格(query:fitemClsCode)
                    del: '/CodeMapping/CD_MapFeeClsGroupFeeCls/Delete',
                    set: '/CodeMapping/CD_MapFeeClsGroupFeeCls/Create'
                  }
                }
              }
            },
            {
              id: '3-3',
              title: '费别信息',
              icon: 'iconfont icon-shezhiduiying',
              path: 'feeTypeInformation',
              children: [],
              viewConfig: {
                viewInfo: {
                  drawerTheads: {
                    payCode: '支付方式代码',
                    payName: '支付方式名称'
                  },
                  title1: '支付方式列表',
                  title2: '',
                  isItem: true, //项目分类?专业组///EnumData/Department
                  isSel: false,
                  isMterials: false, //是否显示材料费
                  isHead: false, //是否显示步
                  active: 2, //显示哪个页面
                  param: {
                    payCode: 'payCode',
                    payName: 'payName'
                  },
                  order: `item.payCode?.includes(searchVal) ||
                  item.payName?.includes(searchVal)`,
                  setInfos: { feeCode: '', feeName: '' }, //点击行传参
                  setInfo: { feeCode: '' }, //点击行传参
                  apiUrls: {
                    leftPort: '/BasicCode/RD_CodePayment/Read', //左边表格
                    rightPort: '/CodeMapping/PageQuery_MapPayModePayment', //右边表格(query:feeCode)
                    del: '/CodeMapping/CD_MapPayModePayment/Delete',
                    set: '/CodeMapping/CD_MapPayModePayment/Create'
                  }
                }
              }
            },
            {
              id: '3-4',
              title: '支付方式信息',
              icon: 'iconfont icon-biaodan',
              path: 'payInformation',
              children: []
            },
            {
              id: '3-5',
              title: '凑整规则信息',
              icon: 'iconfont icon-biaodan',
              path: 'ruleInformation',
              children: []
            }
          ]
        },
        {
          id: '4',
          title: '其他代码对应',
          icon: 'iconfont icon-leixing1',
          children: [
            {
              id: '4-1',
              title: '计算项目公式信息',
              icon: 'iconfont icon-shezhiduiying',
              path: 'otherCodeMapping',
              children: [],
              viewConfig: {
                id: '1',
                theads: {
                  clsName: '项目分类',
                  itemCode: '项目代码',
                  itemName: '项目名称',
                  expressionShow: '表达式'
                },
                operation: '计算公式对应',
                apiUrl: {
                  delete: '/CodeMapping/CD_ItemResultExpression/Delete',
                  get: '/CodeMapping/PageQuery_ItemResultExpression'
                },
                cell_blue: ['itemName'],
                order: `item.clsName?.includes(searchVal) ||
                        item.itemCode?.includes(searchVal) ||
                        item.itemName?.includes(searchVal)`,
                viewInfo: {
                  drawerThead: {
                    itemCode: '项目代码',
                    itemName: '计算项目名称',
                    clsName: '项目分类'
                  },
                  drawerTheads: {
                    clsName: '分类名称',
                    itemCode: '项目代码',
                    itemName: '项目名称'
                  },
                  title: '体检项目列表',
                  title1: '体检项目列表',
                  title2: '',
                  isItem: true, //项目分类?专业组///EnumData/Department
                  isSel: true,
                  isMterials: false, //是否显示材料费
                  isHead: true, //是否显示步
                  active: 1, //显示哪个页面
                  isExpression: true, //是否是计算公式
                  sel1: '选择计算项目',
                  sel2: '设置对应计算公式',
                  orders: `item.itemCode?.includes(searchVal) ||
                  item.itemName?.includes(searchVal)`,
                  orderSels: `item.clsCode?.includes(searchVal)`,
                  order: `item.itemCode?.includes(searchVal) ||
                  item.itemName?.includes(searchVal)`,
                  orderSel: `item.clsCode?.includes(searchVal)`,
                  setInfos: { itemCode: '' }, //点击行传参
                  setInfo: { itemCode: '' }, //点击行传参
                  apiUrls: {
                    viewPort: '/EnumData/Item_ItemCls',
                    optionPort: '/EnumData/ItemCls', //下拉
                    leftPort: '/EnumData/Item_ItemCls', //左边表格
                    rightPort: '', //右边表格(query:itemCode)
                    del: '/CodeMapping/CD_ItemResultExpression/Delete',
                    set: '/CodeMapping/CD_ItemResultExpression/Create',
                    expressionVerify: '/CodeMapping/ItemResultExpressionVerify'
                  }
                }
              }
            },
            {
              id: '4-2',
              title: '体检自动添加组合对应信息',
              icon: 'iconfont icon-shezhiduiying',
              path: 'otherCodeMapping',
              children: [],
              viewConfig: {
                id: '2',
                theads: {
                  clsName: '项目分类',
                  combCode: '组合代码',
                  combName: '组合名称',
                  appendCombCode: '对应组合代码',
                  appendCombName: '对应组合名称',
                  count: '数量'
                },
                operation: '体检组合对应',
                apiUrl: {
                  delete: '/CodeMapping/CUD_CodeCombComb/Delete',
                  get: '/CodeMapping/PageQuery_CodeCombComb'
                },
                cell_blue: ['combName'],
                order: `item.clsName?.includes(searchVal) ||
                        item.combCode?.includes(searchVal) ||
                        item.combName?.includes(searchVal) ||
                        item.appendCombCode?.includes(searchVal) ||
                        item.appendCombName?.includes(searchVal)`,
                viewInfo: {
                  drawerThead: {
                    clsName: '项目分类',
                    combCode: '组合代码',
                    combName: '组合名称',
                    price: '单价',
                    hisCode: 'his代码'
                  },
                  drawerTheads: {
                    clsName: '项目分类',
                    combCode: '组合代码',
                    combName: '组合名称',
                    price: '单价',
                    hisCode: 'his代码'
                  },
                  rightTheads: {
                    clsName: '项目分类',
                    // combCode: "组合代码",
                    // combName: "组合名称",
                    appendCombCode: '对应组合代码',
                    appendCombName: '对应组合名称',
                    price: '单价',
                    hisCode: 'his代码'
                  },
                  title: '体检组合项目列表',
                  title1: '体检对应组合列表',
                  title2: '',
                  isItem: true, //项目分类?专业组///EnumData/Department
                  isSel: true,
                  isMterials: false, //是否显示材料费
                  isHead: true, //是否显示步
                  active: 1, //显示哪个页面
                  isRightTheads: true, //右边表头
                  param: {
                    appendCombCode: 'combCode'
                  },
                  sel1: '选择体检组合项目',
                  sel2: '选择体检对应组合',
                  cell_red: ['price'],
                  orders: `item.combCode?.includes(searchVal) ||
                  item.combName?.includes(searchVal)`,
                  orderSels: `item.clsCode?.includes(searchVal)`,
                  order: `item.combCode?.includes(searchVal) ||
                  item.combName?.includes(searchVal)`,
                  orderSel: `item.clsCode?.includes(searchVal)`,
                  rightOrder: `item.appendCombCode?.includes(searchVal) ||
                  item.appendCombName?.includes(searchVal)`,
                  setInfos: { combCode: '' }, //点击行传参
                  setInfo: { combCode: '' }, //点击行传参
                  apiUrls: {
                    optionPort: '/EnumData/ItemCls', //下拉
                    viewPort: '/EnumData/ItemComb_ItemCls', //页面一
                    leftPort: '/EnumData/ItemComb_ItemCls', //左边表格
                    rightPort: '/CodeMapping/PageQuery_CodeCombComb', //右边表格(query:combCode)
                    del: '/CodeMapping/CUD_CodeCombComb/Delete',
                    set: '/CodeMapping/CUD_CodeCombComb/Create'
                  }
                }
              }
            },
            {
              id: '4-3',
              title: '医生跨科关系设置',
              icon: 'iconfont icon-shezhiduiying',
              path: 'otherCodeMapping',
              children: [],
              viewConfig: {
                id: '3',
                theads: {
                  operatorCode: '人员编号',
                  name: '用户名称',
                  // deptCode: "科室代码",
                  deptName: '科室名称'
                },
                operation: '跨科关系对应',
                apiUrl: {
                  delete: '/CodeMapping/CD_MapDoctorDept/Delete',
                  get: '/CodeMapping/Query_MultiDeptDoctors'
                },
                cell_blue: ['name'],
                order: `item.operCode?.includes(searchVal) ||
                        item.name?.includes(searchVal) ||
                        item.deptCode?.includes(searchVal) ||
                        item.deptName?.includes(searchVal)`,
                viewInfo: {
                  drawerThead: {
                    operatorCode: '人员编号',
                    name: '用户名称'
                    // deptName: "科室名称"
                  },
                  drawerTheads: {
                    deptCode: '科室代码',
                    deptName: '科室名称'
                  },
                  title: '医生列表',
                  title1: '体检对应科室列表',
                  title2: '',
                  isItem: true, //项目分类?专业组///EnumData/Department
                  isSel: false,
                  isMterials: false, //是否显示材料费
                  isHead: true, //是否显示步
                  active: 1, //显示哪个页面
                  param: {
                    deptCode: 'deptCode'
                  },
                  sel1: '选择医生',
                  sel2: '选择科室',
                  orders: `item.operatorCode?.includes(searchVal) ||
                  item.name?.includes(searchVal)`,
                  // orderSels: `item.deptCode?.includes(searchVal)`,
                  order: `item.deptCode?.includes(searchVal) ||
                  item.deptName?.includes(searchVal)`,
                  orderSel: `item.clsCode?.includes(searchVal)`,
                  setInfos: { operatorCode: '' }, //点击行传参
                  setInfo: { operatorCode: '' }, //点击行传参
                  apiUrls: {
                    optionPort: '/EnumData/Department', //下拉
                    viewPort: '/EnumData/Operator', //页面一
                    leftPort: '/EnumData/Department', //左边表格
                    rightPort: '/CodeMapping/PageQuery_MapDoctorDept', //右边表格(query:operatorCode)
                    del: '/CodeMapping/CD_MapDoctorDept/Delete',
                    set: '/CodeMapping/CD_MapDoctorDept/Create'
                  }
                }
              }
            }
          ]
        },
        {
          id: '5',
          title: '重大阳性类代码',
          icon: 'iconfont icon-leixing1',
          children: [
            {
              id: '5-1',
              title: '重大阳性列表',
              icon: 'iconfont icon-biaodan',
              path: 'majorPositiveList',
              children: []
            },
            {
              id: '5-2',
              title: '重大阳性触发关键字列表',
              icon: 'iconfont icon-shezhiduiying',
              path: 'majorPositiveKeywordsList',
              children: [],
              viewConfig: {
                viewInfo: {
                  drawerTheads: {
                    positiveType: '类别',
                    positiveCode: '重大阳性代码',
                    positiveName: '重大阳性名称'
                  },
                  rightTheads: {
                    positiveType: '类别',
                    positiveCode: '重大阳性代码',
                    positiveName: '重大阳性名称'
                    // sortIndex: "排序"
                  },
                  title1: '重大阳性列表',
                  title2: '',
                  isItem: true, //项目分类?专业组///EnumData/Department
                  isSel: false,
                  isMterials: false, //是否显示材料费
                  isRightTheads: true, //右边表头
                  isHead: false, //是否显示步
                  active: 2, //显示哪个页面
                  param: {
                    keywordCode: 'keywordCode',
                    positiveCode: 'positiveCode'
                  },
                  order: `item.positiveCode?.includes(searchVal) ||
                  item.positiveName?.includes(searchVal)`,
                  rightOrder: `item.positiveCode?.includes(searchVal) ||
                  item.positiveName?.includes(searchVal)`,
                  setInfos: { keywordCode: '', keywordName: '' }, //点击行传参
                  setInfo: { keywordCode: '' }, //点击行传参
                  apiUrls: {
                    leftPort: '/MajorPositive/RD_MajorPositive/Read', //左边表格
                    rightPort: '/CodeMapping/PageQuery_KeywordMajorPositive', //右边表格(query:keywordCode)
                    del: '/CodeMapping/CD_KeywordMajorPositive/Delete',
                    set: '/CodeMapping/CD_KeywordMajorPositive/Create'
                  }
                }
              }
            }
          ]
        }
      ],
      tableThead: '',
      viewConfig: {},
      drawerInfo: ''
    };
  },

  mounted() {
    this.getItemCls();
    this.dragControllerDiv();
    console.log(this.G_config);
    if (!this.G_config.physicalMode.includes('普检')) {
      this.muenList[1].children.splice(5, 1);
    }
    if (!this.G_config.physicalMode.includes('职检')) {
      this.muenList[1].children.splice(6, 1);
    }
  },

  methods: {
    // 鼠标拖动改变菜单栏的宽度
    dragControllerDiv() {
      let c = document.getElementById('LeftWrap'); // body监听移动事件
      document
        .getElementById('BasedOnTheCode')
        .addEventListener('mousemove', move); // 鼠标按下事件
      c.addEventListener('mousedown', down); // 鼠标松开事件
      document.getElementById('BasedOnTheCode').addEventListener('mouseup', up); // 是否开启尺寸修改

      let resizeable = false; // 鼠标按下时的坐标，并在修改尺寸时保存上一个鼠标的位置

      let clientX, clientY; // div可修改的最小宽高

      let minW = 8;

      let direc = ''; // 鼠标松开时结束尺寸修改

      function up() {
        resizeable = false;
      }

      // 鼠标按下时开启尺寸修改
      function down(e) {
        let d = getDirection(e); // 当位置为四个边和四个角时才开启尺寸修改

        if (d !== '') {
          resizeable = true;
          direc = d;
          clientX = e.clientX;
          clientY = e.clientY;
        }
      } // 鼠标移动事件

      function move(e) {
        let d = getDirection(e);
        let cursor;
        if (d === '') cursor = 'default';
        else cursor = d + '-resize'; // 修改鼠标显示效果

        c.style.cursor = cursor; // 当开启尺寸修改时，鼠标移动会修改div尺寸

        if (resizeable) {
          // 鼠标按下的位置在右边，修改宽度
          if (Math.max(minW, c.offsetWidth + (e.clientX - clientX)) < 200)
            return;
          if (direc.indexOf('e') !== -1) {
            c.style.width =
              Math.max(minW, c.offsetWidth + (e.clientX - clientX)) + 'px';
            clientX = e.clientX;
          }
        }
      } // 获取鼠标所在div的位置

      function getDirection(ev) {
        let xP, yP, offset, dir;
        dir = '';
        xP = ev.offsetX;
        yP = ev.offsetY;
        offset = 10;
        // if(yP < offset) dir += 'n';
        // else if(yP > c.offsetHeight - offset) dir += 's';
        // if(xP < offset) dir += 'w';
        if (xP > c.offsetWidth - offset) dir += 'e';
        return dir;
      }
    },
    handleOpen(key, keyPath) {
      this.openedMenus = this.$refs.elMenu_Ref.$data.openedMenus;
      console.log(this.openedMenus);
      // console.log(key, keyPath);
    },
    handleClose(key, keyPath) {
      this.openedMenus = this.$refs.elMenu_Ref.$data.openedMenus;
      console.log(this.openedMenus);
      // console.log(key, keyPath);
    },
    handleSelect(index, indexPath) {
      // console.log(index);
      // console.log(indexPath);
    },
    goTo(data) {
      console.log('data: ', data);
      this.path = data.path;
      this.clsCode = data.clsCode;
      this.claName = data.title;
      if (
        data.path === 'otherCodeMapping' ||
        data.path === 'noCommonCombination'
      ) {
        this.viewConfig = data.viewConfig;
        this.$nextTick(() => {
          if (this.$refs.viewConfig) {
            this.$refs.viewConfig.getTableData();
          }
        });
      }
      this.drawerInfo = {};
      if (data.viewConfig) this.drawerInfo = data.viewConfig.viewInfo;
    },
    // 项目分类
    getItemCls() {
      $ajax.post($apiUrls.RD_CodeItemCls + '/Read', []).then((r) => {
        r.data.returnData.map((item, i) => {
          this.muenList[1].children[4].children.push({
            clsCode: item.clsCode,
            id: 'id' + item.clsCode,
            title: item.clsName,
            path: 'physicalExaminationItem',
            viewConfig: {
              viewInfo: {
                drawerTheads: {
                  itemCode: '项目代码',
                  itemName: '项目名称',
                  clsName: '项目分类'
                },
                title1: '体检项目列表',
                title2: '',
                isItem: true, //项目分类?专业组///EnumData/Department
                isSel: true,
                isMterials: false, //是否显示材料费
                isHead: false, //是否显示步
                active: 2, //显示哪个页面
                param: {
                  itemCode: 'itemCode',
                  clsCode: 'clsCode'
                },
                order: `item.itemCode?.includes(searchVal) ||
                      item.itemName?.includes(searchVal)`,
                orderSel: `item.clsCode?.includes(searchVal)`,
                setInfos: { combCode: '' }, //点击行传参
                setInfo: { combCode: '' }, //点击行传参
                apiUrls: {
                  optionPort: '/EnumData/ItemCls', //下拉
                  leftPort: '/EnumData/Item_ItemCls', //左边表格
                  rightPort: '/CodeMapping/pageQuery_ItemComb', //右边表格(query:combCode)
                  del: '/CodeMapping/CD_ItemComb/Delete',
                  set: '/CodeMapping/CD_ItemComb/Create'
                }
              }
            }
          });
          this.muenList[1].children[3].children.push({
            clsCode: item.clsCode,
            id: 'ids' + item.clsCode,
            title: item.clsName,
            path: 'shareThePage',
            viewConfig: {
              viewInfo: {
                drawerTheads: {
                  lisItemCode: '检验项目代码',
                  lisItemCNName: '检验项目名称',
                  lisCombCode: '检验组合代码',
                  lisCombCNName: '检验组合名称'
                },
                title1: '检验项目列表',
                title2: '',
                isItem: true, //项目分类?专业组///EnumData/Department
                isSel: false,
                isMterials: false, //是否显示材料费
                isHead: false, //是否显示步
                active: 2, //显示哪个页面
                isOnly: true,
                param: {
                  pageSize: 'pageSize',
                  pageNumber: 'pageNumber',
                  itemCode: 'itemCode',
                  keyword: 'keyword'
                  // itemCode: "itemCode",
                  // itemName: "itemName",
                  // lisItemCode: "lisItemCode",
                  // lisItemName: "lisItemName",
                  // lisRoomCode: "lisRoomCode",
                  // lisRoomName: "lisRoomName"
                },
                order: `item.lisItemCode?.includes(searchVal) ||
                      item.lisItemCNName?.includes(searchVal)`, //第二页输入框搜索
                orderSel: `item.lisRoomCode?.includes(searchVal)`, //第二页下拉框搜索
                setInfos: { itemCode: '', itemName: '' }, //点击对应详情传参
                setInfo: { itemCode: '' }, //点击行传参
                apiUrls: {
                  optionPort: '/EnumData/ItemCls', //下拉
                  leftPort: $apiUrls.GetCodeLisItems, //左边表格
                  rightPort: $apiUrls.GetMapCodeLisItems, //右边表格(query:combCode)
                  del: $apiUrls.RemoveMapCodeLisItems,
                  set: $apiUrls.SaveMapCodeLisItems
                }
              }
            }
          });
        });
      });
    }
  }
});
</script>
<style lang="less" scoped>
.basedOnTheCode {
  display: flex;
  .left {
    width: 320px;
    background: white;
    p {
      font-size: 16px;
      color: #2d3436;
      line-height: 48px;
      margin-left: 10px;
    }
    img {
      width: 16px;
    }
    .icon {
      margin-right: 8px;
      font-size: 18px;
    }
    .icons {
      font-size: 16px;
      margin-right: 8px;
    }
    /deep/.el-submenu__title {
      font-size: 16px;
      height: 30px;
      line-height: 30px;
      // background-color: #eee;
      // border-bottom: 1px solid #ccc;
    }
    .el-submenu .el-menu-item {
      height: 30px;
      line-height: 30px;
    }
    .el-submenu .el-menu-item:focus,
    // .el-menu-item:hover,
    .el-menu-item.is-active {
      outline: 0;
      color: #fff;
      background-color: #1770df;
      border-radius: 4px;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    flex: 1;
    flex-shrink: 0;
    // width: calc(100% - 360px);
    background: white;
    margin-left: 20px;
    overflow: auto;
    padding: 0 15px 15px 15px;
  }
}
</style>
<style lang="less">
.basedOnTheCode {
  .el-menu--inline {
    // background: #cde6fa;
    // flex: 1;
    // width: calc(100% - 320px);
    background: white;
    // margin-left: 20px;
    padding: 0 4px;
  }
  .resize {
    cursor: col-resize;
    position: relative;
    top: 45%;
    background-color: #d6d6d6;
    border-radius: 5px;
    margin-top: -10px;
    width: 10px;
    height: 50px;
    background-size: cover;
    background-position: center;
    width: 10px;
    font-size: 32px;
    color: white;
  }

  .resize:hover {
    color: #444444;
  }
  .el-table {
    color: #2d3436;
  }
  /* 表格内容颜色 */
  .el-table th.el-table__cell {
    background-color: #d1e2f9;
    color: #717c86;
  }
  .el-table__body tbody tr:nth-child(even) td {
    background-color: #e7f0fb;
  }
}
</style>
